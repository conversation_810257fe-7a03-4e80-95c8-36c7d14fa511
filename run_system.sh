#!/bin/bash

echo "============================================"
echo "    PROJE YÖNETİM SİSTEMİ BAŞLATICI"
echo "============================================"
echo

echo "Agent'lar başlatılıyor..."
echo

echo "Task Generator Agent başlatılıyor (Port 8004)..."
python run_agent.py task_generator &
TASK_PID=$!
sleep 3

echo "Scheduler Agent başlatılıyor (Port 8005)..."
python run_agent.py scheduler &
SCHEDULER_PID=$!
sleep 3

echo
echo "Agent'lar başlatıldı! 5 saniye bekleniyor..."
sleep 5

echo
echo "Client başlatılıyor..."
python project_manager_client.py

echo
echo "Sistem kapatılıyor..."
kill $TASK_PID $SCHEDULER_PID 2>/dev/null
echo "Agent'lar durduruldu."
