"""
Scheduler Agent - Task'ları alıp zaman çizelgesi oluşturur
"""
from collections.abc import AsyncGenerator
from acp_sdk.models import Message, MessagePart
from acp_sdk.server import Context, RunYield, RunYieldResume, Server
from config import OPENROUTER_API_KEY
import json
import logging
from datetime import datetime, timedelta
import openai

logger = logging.getLogger(__name__)

# OpenAI client for OpenRouter
client = openai.OpenAI(
    api_key=OPENROUTER_API_KEY,
    base_url="https://openrouter.ai/api/v1"
)

server = Server()

@server.agent()
async def scheduler(input: list[Message], context: Context) -> AsyncGenerator[RunYield, RunYieldResume]:
    """
    Task listesini alıp zaman çizelgesi oluşturan agent.
    
    Input: JSON formatında task listesi
    Output: JSON formatında zaman çizelgesi
    """
    
    tasks_json = input[0].parts[0].content
    
    # Bugünün tarihini al
    today = datetime.now().strftime("%Y-%m-%d")
    
    schedule_prompt = f"""
    Task Listesi: {tasks_json}
    
    Bu task listesini optimal bir zaman çizelgesine dönüştür. Başlangıç tarihi: {today}
    
    Şu faktörleri göz önünde bulundur:
    1. Task bağımlılıkları - bağımlı task'lar önce tamamlanmalı
    2. Öncelik seviyeleri - High öncelikli task'lar önce
    3. Paralel çalışma - bağımsız task'lar paralel yapılabilir
    4. Günlük 8 saatlik çalışma
    5. Hafta sonu tatil (Cumartesi-Pazar)
    
    Her task için şu bilgileri içer:
    - Başlangıç tarihi ve saati
    - Bitiş tarihi ve saati
    - Süre (saat)
    - Paralel çalışılabilecek diğer task'lar
    
    Sonucu şu JSON formatında döndür:
    {{
        "project_name": "Proje Adı",
        "schedule_created": "{today}",
        "total_duration_days": 0,
        "working_hours_per_day": 8,
        "schedule": [
            {{
                "task_id": "T001",
                "task_name": "Task Adı",
                "start_date": "2024-01-15",
                "start_time": "09:00",
                "end_date": "2024-01-15",
                "end_time": "17:00",
                "duration_hours": 8,
                "day_of_week": "Monday",
                "parallel_tasks": ["T002"],
                "status": "scheduled"
            }}
        ],
        "milestones": [
            {{
                "name": "Milestone Adı",
                "date": "2024-01-20",
                "completed_tasks": ["T001", "T002"]
            }}
        ]
    }}
    """
    
    try:
        # LiteLLM ile doğrudan çağrı yap
        system_prompt = """Sen deneyimli bir proje planlayıcısı ve zaman yönetimi uzmanısın.
        Task listelerini alıp bunları optimal bir zaman çizelgesine dönüştürebilirsin.
        Task bağımlılıkları, öncelik seviyeleri, paralel çalışma imkanlarını dikkate alırsın.
        Sonucu JSON formatında döndürmelisin."""

        messages = [
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": schedule_prompt}
        ]

        response = client.chat.completions.create(
            model="openai/gpt-4o-mini",
            messages=messages,
            max_tokens=4096,
            temperature=0.7
        )

        result = response.choices[0].message.content
        logger.info("Schedule generation completed successfully")
        logger.info(f"Generated schedule: {result}")

        # JSON formatını kontrol et
        try:
            json.loads(result)
            response_content = result
        except json.JSONDecodeError:
            # JSON formatı bozuksa düzelt
            response_content = f'{{"error": "JSON format hatası", "raw_output": "{result}"}}'

        yield Message(parts=[MessagePart(content=response_content)])

    except Exception as e:
        logger.error(f"Schedule generation failed: {e}")
        error_response = f'{{"error": "Schedule generation failed: {str(e)}"}}'
        yield Message(parts=[MessagePart(content=error_response)])

if __name__ == "__main__":
    server.run(port=8003)
