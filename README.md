# Proje <PERSON>

ACP (Agent Communication Protocol) tabanlı proje yönetim sistemi. <PERSON>je fiki<PERSON> alıp detaylı task listesi ve zaman çizelgesi oluşturur.

## Sistem Bileşenleri

### 🤖 Agent'lar
- **Task Generator Agent** (Port 8002): <PERSON><PERSON> fiki<PERSON> detaylı task listesine dönüştürür
- **Scheduler Agent** (Port 8003): Task'ları alıp optimal zaman çizelgesi oluşturur

### 💻 Client
- **Project Manager Client**: Ku<PERSON><PERSON><PERSON>ıdan proje fikri alıp agent'larla iletişim kurar

## Kurulum

### 1. <PERSON><PERSON><PERSON><PERSON><PERSON>
```bash
pip install -r requirements.txt
```

### 2. OpenRouter API Key

#### Yöntem 1: .env dosyası (Önerilen)
Proje klasöründe `.env` dosyası oluşturun:
```
OPENROUTER_API_KEY=your-api-key-here
```

#### Yöntem 2: Environment Variable
```bash
# Windows
set OPENROUTER_API_KEY=your-api-key-here

# Linux/Mac
export OPENROUTER_API_KEY=your-api-key-here
```

### 3. Sistemi Başlatma

#### Hızlı Başlatma (Önerilen)
```bash
# Windows
run_system.bat

# Linux/Mac
./run_system.sh
```

#### Manuel Başlatma
Farklı terminal'lerde:

```bash
# Terminal 1 - Task Generator Agent
python run_agent.py task_generator

# Terminal 2 - Scheduler Agent
python run_agent.py scheduler

# Terminal 3 - Client
python project_manager_client.py
```

#### Sistem Testi
```bash
python demo.py
```

## Kullanım

1. Sistem başlatıldıktan sonra proje fikrinizi detaylı olarak açıklayın
2. Sistem otomatik olarak:
   - Detaylı task listesi oluşturacak
   - Optimal zaman çizelgesi hazırlayacak
3. Sonuçları terminal'de görüntüleyebilirsiniz

### Örnek Proje Fikri
```
E-ticaret web sitesi geliştirmek istiyorum. 
Kullanıcı kayıt/giriş sistemi, ürün kataloğu, 
sepet yönetimi ve ödeme sistemi olacak.
React frontend ve Node.js backend kullanacağım.
```

## Özellikler

### Task Generator
- ✅ Proje fikirlerini detaylı task'lara böler
- ✅ Her task için süre tahmini
- ✅ Öncelik seviyesi belirleme
- ✅ Gerekli beceriler listesi
- ✅ Task bağımlılıkları
- ✅ Kategori sınıflandırması

### Scheduler
- ✅ Task bağımlılıklarını dikkate alır
- ✅ Öncelik seviyelerine göre sıralama
- ✅ Paralel çalışma imkanları
- ✅ Günlük 8 saatlik çalışma planı
- ✅ Hafta sonu tatil hesaplaması
- ✅ Milestone'lar

## Teknik Detaylar

### Kullanılan Teknolojiler
- **ACP SDK**: Agent'lar arası iletişim
- **CrewAI**: Agent framework
- **OpenRouter**: LLM API
- **LiteLLM**: Model abstraction
- **Colorama**: Terminal renklendirme

### Port'lar
- 8004: Task Generator Agent
- 8005: Scheduler Agent

### Desteklenen Modeller
- meta-llama/llama-3.3-70b-instruct:free (varsayılan - ücretsiz)
- Diğer OpenRouter modelleri config.py'dan değiştirilebilir

### ⚠️ Önemli Notlar
- **Free model sınırları**: meta-llama/llama-3.3-70b-instruct:free modeli dakikada 1 request ile sınırlıdır
- Rate limit hatası alırsanız 1 dakika bekleyip tekrar deneyin
- Daha hızlı kullanım için ücretli model kullanabilirsiniz (config.py'da değiştirin)

## Sorun Giderme

### Agent Başlatma Sorunları
- Port'ların kullanımda olmadığından emin olun
- OpenRouter API key'in doğru ayarlandığını kontrol edin
- Internet bağlantınızı kontrol edin

### JSON Format Hataları
- Agent'lar bazen JSON formatında olmayan çıktı verebilir
- Sistem otomatik olarak hata mesajı gösterir
- Farklı bir proje fikri ile tekrar deneyin

## Geliştirme

### Yeni Agent Ekleme
1. `config.py`'dan LLM konfigürasyonu alın
2. ACP server pattern'ini kullanın
3. `start_system.py`'a yeni agent'ı ekleyin

### Model Değiştirme
`config.py` dosyasında `DEFAULT_MODEL` değişkenini güncelleyin.

## Lisans

Bu proje MIT lisansı altında lisanslanmıştır.
