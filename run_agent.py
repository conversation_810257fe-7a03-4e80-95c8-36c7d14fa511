"""
Agent ç<PERSON>ı<PERSON><PERSON><PERSON><PERSON>ı - SSL sorununu çözer
"""
import os
import sys

# SSL environment variable'ını temizle
os.environ.pop('SSL_CERT_FILE', None)

if len(sys.argv) < 2:
    print("Kullanım: python run_agent.py <agent_script>")
    sys.exit(1)

agent_script = sys.argv[1]

# Agent'ı import et ve çalıştır
if agent_script == "task_generator":
    import task_generator_agent
    task_generator_agent.server.run(port=8004)
elif agent_script == "scheduler":
    import scheduler_agent
    scheduler_agent.server.run(port=8005)
else:
    print(f"Bilinmeyen agent: {agent_script}")
    sys.exit(1)
