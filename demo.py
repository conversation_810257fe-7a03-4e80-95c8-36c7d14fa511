"""
Demo - Sistem çalışıyor mu test et
"""
import os
import asyncio
from colorama import Fore, init

# SSL sorununu çöz
os.environ.pop('SSL_CERT_FILE', None)

# Colorama'yı başlat
init(autoreset=True)

async def demo():
    print(f"{Fore.CYAN}{'='*60}")
    print(f"{Fore.CYAN}🎯 PROJE YÖNETİM SİSTEMİ DEMO")
    print(f"{Fore.CYAN}{'='*60}")
    
    print(f"{Fore.WHITE}Bu demo sisteminizin çalışıp çalışmadığını test eder.")
    print(f"{Fore.YELLOW}Agent'ların çalı<PERSON>tığından emin olun:")
    print(f"{Fore.WHITE}  - Task Generator Agent (Port 8004)")
    print(f"{Fore.WHITE}  - Scheduler Agent (Port 8005)")
    print(f"{Fore.CYAN}{'-'*60}")
    
    try:
        from acp_sdk.client import Client
        from acp_sdk.models import Message, MessagePart
        
        print(f"{Fore.GREEN}✅ ACP SDK yüklendi")
        
        # Agent'lara bağlan
        print(f"{Fore.CYAN}🔗 Agent'lara bağlanılıyor...")
        
        async with Client(base_url="http://localhost:8004") as task_client:
            print(f"{Fore.GREEN}✅ Task Generator Agent'a bağlandı")
            
            # Agent listesini al
            agents = []
            async for agent in task_client.agents():
                agents.append(agent)
                print(f"{Fore.GREEN}   📋 Agent: {agent.name}")
            
            if agents:
                print(f"{Fore.CYAN}📤 Test mesajı gönderiliyor...")
                
                # Basit test
                test_message = [Message(parts=[MessagePart(content="Basit bir blog web sitesi projesi")])]
                
                try:
                    response = await task_client.run_sync(
                        agent=agents[0].name,
                        input=test_message
                    )
                    
                    result = response.output[0].parts[0].content
                    print(f"{Fore.GREEN}✅ Task Generator yanıtı alındı!")
                    print(f"{Fore.WHITE}Yanıt uzunluğu: {len(result)} karakter")
                    
                    if "error" in result.lower():
                        print(f"{Fore.YELLOW}⚠️  Yanıtta hata var (muhtemelen rate limit)")
                        print(f"{Fore.WHITE}İlk 200 karakter: {result[:200]}...")
                    else:
                        print(f"{Fore.GREEN}✅ Başarılı yanıt!")
                        print(f"{Fore.WHITE}İlk 200 karakter: {result[:200]}...")
                        
                except Exception as e:
                    print(f"{Fore.RED}❌ Test mesajı hatası: {e}")
            else:
                print(f"{Fore.RED}❌ Agent bulunamadı")
        
        print(f"\n{Fore.CYAN}🔗 Scheduler Agent test ediliyor...")
        
        async with Client(base_url="http://localhost:8005") as scheduler_client:
            print(f"{Fore.GREEN}✅ Scheduler Agent'a bağlandı")
            
            agents = []
            async for agent in scheduler_client.agents():
                agents.append(agent)
                print(f"{Fore.GREEN}   📅 Agent: {agent.name}")
            
            print(f"{Fore.GREEN}✅ Tüm bağlantılar başarılı!")
        
        print(f"\n{Fore.GREEN}{'='*60}")
        print(f"{Fore.GREEN}🎉 SİSTEM ÇALIŞIYOR!")
        print(f"{Fore.GREEN}{'='*60}")
        print(f"{Fore.WHITE}Artık ana client'ı çalıştırabilirsiniz:")
        print(f"{Fore.CYAN}  python project_manager_client.py")
        print(f"{Fore.WHITE}veya")
        print(f"{Fore.CYAN}  run_system.bat (Windows)")
        print(f"{Fore.CYAN}  ./run_system.sh (Linux/Mac)")
        
    except Exception as e:
        print(f"{Fore.RED}❌ Demo hatası: {e}")
        print(f"{Fore.YELLOW}Agent'ların çalıştığından emin olun!")

if __name__ == "__main__":
    asyncio.run(demo())
