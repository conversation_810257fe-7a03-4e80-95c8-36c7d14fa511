# 🚀 Proje Planlama Agent <PERSON><PERSON><PERSON>, client'tan gelen proje fikirlerini alıp detaylı task listesi ve zaman planlaması oluşturan bir agent sistemidir.

## 🏗️ Sistem Mimarisi

### Agentlar:
1. **Task Generator Agent** (Port 8001) - <PERSON><PERSON> fi<PERSON> detaylı görev listelerine dönüştürür
2. **Time Planner Agent** (Port 8003) - Görev listelerini zaman planlamasına çevirir
3. **Koordinatör Agent** - İki agent'ı yönetir ve sonuçları güzel formatta sunar

### Model Konfigürasyonu:
- **OpenRouter o1-mini** modeli kullanılıyor
- Her agent kendi dosyasında tanımlı

## 🛠️ Kurulum

1. Bağımlılıkları yükle:
```bash
pip install -r requirements.txt
```

2. OpenRouter API key'ini ayarla:
   - Her dosyada `your-openrouter-api-key` kısmın<PERSON> gerçek API key ile de<PERSON>

## 🚀 Kullanım

1. **Task Generator Agent'ı başlat:**
```bash
python "2. CrewAI via Server.py"
```

2. **Time Planner Agent'ı başlat:**
```bash
python "4. smolagents ACP.py"
```

3. **Ana client'ı çalıştır:**
```bash
python "6. ACPCallingAgent.py"
```

4. Proje fikrinizi girin ve sistem otomatik olarak:
   - Detaylı task listesi oluşturacak
   - Zaman planlaması yapacak
   - Güzel formatlanmış sonuç sunacak

## 📋 Örnek Çıktı

Sistem şu formatta çıktı verir:
- 🎯 Proje özeti
- 📝 Detaylı görev listesi
- ⏰ Zaman planlaması
- 🎯 Milestone'lar
- ⚠️ Risk faktörleri