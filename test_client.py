"""
Basit test client - Agent'ları test eder
"""
import asyncio
import os
from acp_sdk.client import Client
from acp_sdk.models import Message, MessagePart

# SSL sorununu çöz
os.environ.pop('SSL_CERT_FILE', None)

async def test_agents():
    """Agent'ları test et"""
    
    print("🔍 Agent'ları test ediliyor...")
    
    try:
        # Task Generator Agent test
        print("📋 Task Generator Agent test ediliyor...")
        async with Client(base_url="http://localhost:8004") as task_client:
            agents = []
            async for agent in task_client.agents():
                agents.append(agent)
                print(f"✅ Bulunan agent: {agent.name}")
            
            if agents:
                # Test mesajı gönder
                test_message = [Message(parts=[MessagePart(content="Basit bir web sitesi projesi")])]
                response = await task_client.run_sync(
                    agent=agents[0].name,
                    input=test_message
                )
                print(f"📋 Task Generator Response: {response.output[0].parts[0].content[:100]}...")
            else:
                print("❌ Task Generator Agent bulunamadı")
        
        # Scheduler Agent test
        print("\n📅 Scheduler Agent test ediliyor...")
        async with Client(base_url="http://localhost:8005") as scheduler_client:
            agents = []
            async for agent in scheduler_client.agents():
                agents.append(agent)
                print(f"✅ Bulunan agent: {agent.name}")
            
            if agents:
                # Test mesajı gönder
                test_tasks = '{"project_name": "Test", "tasks": [{"id": "T001", "name": "Test Task", "estimated_hours": 8}]}'
                test_message = [Message(parts=[MessagePart(content=test_tasks)])]
                response = await scheduler_client.run_sync(
                    agent=agents[0].name,
                    input=test_message
                )
                print(f"📅 Scheduler Response: {response.output[0].parts[0].content[:100]}...")
            else:
                print("❌ Scheduler Agent bulunamadı")
        
        print("\n✅ Tüm testler tamamlandı!")
        
    except Exception as e:
        print(f"❌ Test hatası: {e}")

if __name__ == "__main__":
    asyncio.run(test_agents())
