"""
Basit Client - <PERSON><PERSON> calling olmadan direkt agent'larla konuşur
"""
import asyncio
import json
import os
from acp_sdk.client import Client
from acp_sdk.models import Message, MessagePart
from colorama import Fore, Style, init

# SSL sorununu çöz
os.environ.pop('SSL_CERT_FILE', None)

# Colorama'yı başlat
init(autoreset=True)

class SimpleProjectManager:
    def __init__(self):
        self.task_generator_client = None
        self.scheduler_client = None
    
    async def initialize(self):
        """Agent'lara bağlan"""
        try:
            print(f"{Fore.CYAN}🚀 Basit Proje Yönetim Sistemi başlatılıyor...")
            
            self.task_generator_client = Client(base_url="http://localhost:8004")
            self.scheduler_client = Client(base_url="http://localhost:8005")
            
            print(f"{Fore.GREEN}✅ Agent bağlantıları kuruldu")
            return True
            
        except Exception as e:
            print(f"{Fore.RED}❌ Başlatma hatası: {e}")
            return False
    
    async def cleanup(self):
        """Bağlantıları temizle"""
        try:
            if self.task_generator_client:
                await self.task_generator_client.__aexit__(None, None, None)
            if self.scheduler_client:
                await self.scheduler_client.__aexit__(None, None, None)
            print(f"{Fore.YELLOW}🧹 Bağlantılar temizlendi")
        except Exception as e:
            print(f"{Fore.RED}❌ Temizleme hatası: {e}")
    
    def get_project_idea(self):
        """Kullanıcıdan proje fikri al"""
        print(f"\n{Fore.CYAN}{'='*60}")
        print(f"{Fore.CYAN}🎯 BASİT PROJE YÖNETİM SİSTEMİ")
        print(f"{Fore.CYAN}{'='*60}")
        print(f"{Fore.WHITE}Proje fikrinizi detaylı olarak açıklayın:")
        print(f"{Fore.YELLOW}Örnek: 'E-ticaret web sitesi geliştirmek istiyorum. Kullanıcı kayıt/giriş, ürün kataloğu, sepet, ödeme sistemi olacak.'")
        print(f"{Fore.CYAN}{'-'*60}")
        
        project_idea = input(f"{Fore.WHITE}Proje Fikri: {Style.RESET_ALL}")
        return project_idea.strip()
    
    def display_tasks(self, tasks_result):
        """Task listesini güzel formatta göster"""
        try:
            # JSON parse etmeye çalış
            if tasks_result.startswith('{'):
                tasks_data = json.loads(tasks_result)
                
                print(f"\n{Fore.GREEN}{'='*60}")
                print(f"{Fore.GREEN}📋 OLUŞTURULAN TASK LİSTESİ")
                print(f"{Fore.GREEN}{'='*60}")
                
                print(f"{Fore.CYAN}Proje: {tasks_data.get('project_name', 'Bilinmiyor')}")
                print(f"{Fore.CYAN}Toplam Tahmini Süre: {tasks_data.get('total_estimated_hours', 0)} saat")
                print(f"{Fore.CYAN}{'-'*60}")
                
                for task in tasks_data.get('tasks', []):
                    print(f"\n{Fore.YELLOW}🔸 {task.get('id', 'N/A')}: {task.get('name', 'N/A')}")
                    print(f"   📝 {task.get('description', 'N/A')}")
                    print(f"   ⏱️  Süre: {task.get('estimated_hours', 0)} saat")
                    print(f"   🎯 Öncelik: {task.get('priority', 'N/A')}")
                    if task.get('skills'):
                        print(f"   🛠️  Beceriler: {', '.join(task.get('skills', []))}")
                    print(f"   📂 Kategori: {task.get('category', 'N/A')}")
                    if task.get('dependencies'):
                        print(f"   🔗 Bağımlılıklar: {', '.join(task.get('dependencies', []))}")
            else:
                print(f"\n{Fore.GREEN}📋 TASK LİSTESİ:")
                print(f"{Fore.WHITE}{tasks_result}")
                        
        except json.JSONDecodeError:
            print(f"\n{Fore.GREEN}📋 TASK LİSTESİ:")
            print(f"{Fore.WHITE}{tasks_result}")
    
    def display_schedule(self, schedule_result):
        """Zaman çizelgesini güzel formatta göster"""
        try:
            # JSON parse etmeye çalış
            if schedule_result.startswith('{'):
                schedule_data = json.loads(schedule_result)
                
                print(f"\n{Fore.MAGENTA}{'='*60}")
                print(f"{Fore.MAGENTA}📅 ZAMAN ÇİZELGESİ")
                print(f"{Fore.MAGENTA}{'='*60}")
                
                print(f"{Fore.CYAN}Proje: {schedule_data.get('project_name', 'Bilinmiyor')}")
                print(f"{Fore.CYAN}Toplam Süre: {schedule_data.get('total_duration_days', 0)} gün")
                print(f"{Fore.CYAN}Günlük Çalışma: {schedule_data.get('working_hours_per_day', 8)} saat")
                print(f"{Fore.CYAN}{'-'*60}")
                
                for item in schedule_data.get('schedule', []):
                    print(f"\n{Fore.YELLOW}📌 {item.get('task_id', 'N/A')}: {item.get('task_name', 'N/A')}")
                    print(f"   📅 Tarih: {item.get('start_date', 'N/A')} ({item.get('day_of_week', 'N/A')})")
                    print(f"   🕐 Saat: {item.get('start_time', 'N/A')} - {item.get('end_time', 'N/A')}")
                    print(f"   ⏱️  Süre: {item.get('duration_hours', 0)} saat")
                    if item.get('parallel_tasks'):
                        print(f"   🔄 Paralel: {', '.join(item.get('parallel_tasks', []))}")
                
                # Milestone'ları göster
                milestones = schedule_data.get('milestones', [])
                if milestones:
                    print(f"\n{Fore.GREEN}🎯 MİLESTONE'LAR:")
                    for milestone in milestones:
                        print(f"   🏁 {milestone.get('name', 'N/A')} - {milestone.get('date', 'N/A')}")
            else:
                print(f"\n{Fore.MAGENTA}📅 ZAMAN ÇİZELGESİ:")
                print(f"{Fore.WHITE}{schedule_result}")
                        
        except json.JSONDecodeError:
            print(f"\n{Fore.MAGENTA}📅 ZAMAN ÇİZELGESİ:")
            print(f"{Fore.WHITE}{schedule_result}")
    
    async def run(self):
        """Ana çalışma döngüsü"""
        if not await self.initialize():
            return
        
        try:
            while True:
                project_idea = self.get_project_idea()
                
                if not project_idea:
                    print(f"{Fore.YELLOW}⚠️  Proje fikri boş olamaz!")
                    continue
                
                if project_idea.lower() in ['exit', 'quit', 'çıkış']:
                    break
                
                print(f"\n{Fore.CYAN}🔄 Task'lar oluşturuluyor...")
                
                # Task Generator'a direkt mesaj gönder
                try:
                    async with self.task_generator_client as client:
                        # Agent listesini al
                        agents = []
                        async for agent in client.agents():
                            agents.append(agent)
                        
                        if agents:
                            # Task oluşturma mesajı
                            message = [Message(parts=[MessagePart(content=project_idea)])]
                            response = await client.run_sync(
                                agent=agents[0].name,
                                input=message
                            )
                            
                            tasks_result = response.output[0].parts[0].content
                            self.display_tasks(tasks_result)
                            
                            print(f"\n{Fore.CYAN}🔄 Zaman çizelgesi oluşturuluyor...")
                            
                            # Scheduler'a task listesini gönder
                            async with self.scheduler_client as scheduler:
                                scheduler_agents = []
                                async for agent in scheduler.agents():
                                    scheduler_agents.append(agent)
                                
                                if scheduler_agents:
                                    schedule_message = [Message(parts=[MessagePart(content=tasks_result)])]
                                    schedule_response = await scheduler.run_sync(
                                        agent=scheduler_agents[0].name,
                                        input=schedule_message
                                    )
                                    
                                    schedule_result = schedule_response.output[0].parts[0].content
                                    self.display_schedule(schedule_result)
                                    
                                    print(f"\n{Fore.GREEN}✅ Proje planı tamamlandı!")
                                else:
                                    print(f"{Fore.RED}❌ Scheduler agent bulunamadı")
                        else:
                            print(f"{Fore.RED}❌ Task generator agent bulunamadı")
                            
                except Exception as e:
                    print(f"{Fore.RED}❌ İşlem hatası: {e}")
                    if "rate limit" in str(e).lower() or "429" in str(e):
                        print(f"{Fore.YELLOW}⚠️  Rate limit hatası - 1 dakika bekleyin")
                
                print(f"\n{Fore.YELLOW}Yeni proje için devam etmek ister misiniz? (Enter) veya çıkmak için 'exit' yazın.")
                
        except KeyboardInterrupt:
            print(f"\n{Fore.YELLOW}👋 Sistem kapatılıyor...")
        except Exception as e:
            print(f"{Fore.RED}❌ Hata: {e}")
        finally:
            await self.cleanup()

async def main():
    """Ana fonksiyon"""
    client = SimpleProjectManager()
    await client.run()

if __name__ == "__main__":
    asyncio.run(main())
