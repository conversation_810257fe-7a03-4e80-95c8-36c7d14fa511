from crewai import Crew, Task, Agent, LLM
from crewai_tools import RagTool

from collections.abc import AsyncGenerator
from acp_sdk.models import Message, MessagePart
from acp_sdk.server import Context, RunYield, RunYieldResume, Server

llm = LLM(model="openrouter/openai/gpt-4o-mini", base_url="https://openrouter.ai/api/v1", max_tokens=4000)
config = {
    "llm": {
        "provider": "openrouter",
        "config": {
            "model": "openai/gpt-4o-mini",
        }
    }
}


task_generator_agent = Agent(
    role="Project Task Generator",
    goal="Generate detailed task lists from project ideas",
    backstory="""You are an expert project manager who breaks down project ideas into actionable tasks.
    You create comprehensive task lists with priorities, time estimates, and dependencies.""",
    verbose=True,
    allow_delegation=False,
    llm=llm,
    max_retry_limit=5
)

import logging 
logger = logging.getLogger(__name__)

server = Server()

@server.agent()
async def task_generator(input: list[Message], context: Context) -> AsyncGenerator[<PERSON><PERSON><PERSON>, Run<PERSON>ieldResume]:
    "This agent takes a project idea and generates a detailed task list with priorities and time estimates."

    project_idea = input[0].parts[0].content

    task1 = Task(
         description=f"""
         Proje Fikri: {project_idea}

         Bu proje fikrini analiz ederek detaylı bir task listesi oluştur. Her task için:
         1. Task adı (kısa ve net)
         2. Açıklama
         3. Öncelik (Yüksek/Orta/Düşük)
         4. Tahmini süre (saat)
         5. Bağımlılıklar

         Sonucu düzenli bir formatta sun.
         """,
         expected_output = "Detaylı task listesi ve proje planı",
         agent=task_generator_agent
    )
    crew = Crew(agents=[task_generator_agent], tasks=[task1], verbose=True)

    task_output = await crew.kickoff_async()
    logger.info("Task generation completed successfully")
    logger.info(task_output)
    yield Message(parts=[MessagePart(content=str(task_output))])

if __name__ == "__main__":
    server.run(port=8001)