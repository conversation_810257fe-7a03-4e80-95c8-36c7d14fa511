from collections.abc import AsyncGenerator
from acp_sdk.models import Message, MessagePart
from acp_sdk.server import Context, <PERSON><PERSON>ield, RunYieldResume, Server
from smolagents import ToolCallingAgent, ToolCollection, CodeAgent, DuckDuckGoSearchTool, LiteLLMModel, VisitWebpageTool
import logging 
import os
from dotenv import load_dotenv
load_dotenv()

# OpenRouter API configuration
OPENROUTER_API_KEY = os.getenv("OPENROUTER_API_KEY")
server = Server()

model = LiteLLMModel(
    model_id="openrouter/openai/gpt-4o-mini",
    api_base="https://openrouter.ai/api/v1",
    api_key=OPENROUTER_API_KEY,
    num_ctx=4000,
)

@server.agent()
async def time_planner(input: list[Message], context: Context) -> AsyncGenerator[RunYield, RunYieldResume]:
    "This agent takes a task list and creates a detailed time schedule with milestones and deadlines."
    agent = ToolCallingAgent(tools=[], model=model)

    task_list = input[0].parts[0].content

    prompt = f"""
    Task Listesi: {task_list}

    Bu task listesini alarak detaylı bir zaman planlaması oluştur:

    1. Toplam proje süresi tahmini
    2. Her task için başlangıç ve bitiş tarihleri
    3. Kritik yol analizi
    4. Milestone'lar ve ara hedefler
    5. Risk faktörleri ve buffer süreleri
    6. Kaynak dağılımı önerileri

    Sonucu düzenli bir takvim formatında sun.
    """

    response = agent.run(prompt)

    yield Message(parts=[MessagePart(content=str(response))])


if __name__ == "__main__":
    server.run(port=8003)