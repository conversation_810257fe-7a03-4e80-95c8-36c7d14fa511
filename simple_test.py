"""
Çok basit test
"""
import os
os.environ.pop('SSL_CERT_FILE', None)

print("🚀 Sistem testi başlıyor...")

try:
    from config import OPENROUTER_API_KEY
    print(f"✅ Config yüklendi, API Key: {'Var' if OPENROUTER_API_KEY else 'Yok'}")
    
    import openai
    print("✅ OpenAI import edildi")
    
    # OpenAI client test
    client = openai.OpenAI(
        api_key=OPENROUTER_API_KEY,
        base_url="https://openrouter.ai/api/v1"
    )
    print("✅ OpenAI client oluşturuldu")
    
    # Basit API testi
    response = client.chat.completions.create(
        model="meta-llama/llama-3.3-70b-instruct:free",
        messages=[{"role": "user", "content": "Mer<PERSON><PERSON>, sadece 'Test başarılı' diye cevap ver."}],
        max_tokens=50
    )
    
    print(f"✅ API testi başarılı: {response.choices[0].message.content}")
    
except Exception as e:
    print(f"❌ Hata: {e}")
    import traceback
    traceback.print_exc()

print("🏁 Test tamamlandı")
