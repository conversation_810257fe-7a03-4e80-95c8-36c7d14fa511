"""
Project Manager Client - <PERSON>je yönetim sistemi için ana client
"""
import asyncio
import json
import os
from acp_sdk.client import Client
from fastacp import AgentCollection, ACPCallingAgent
from config import get_openrouter_model
from colorama import Fore, Style, init

# SSL sorununu çöz
os.environ.pop('SSL_CERT_FILE', None)

# Colorama'yı başlat
init(autoreset=True)

class ProjectManagerClient:
    def __init__(self):
        self.model = get_openrouter_model()
        self.task_generator_client = None
        self.scheduler_client = None
        self.agent_collection = None
        self.acp_agent = None
    
    async def initialize(self):
        """Agent'ları başlat ve bağlantıları kur"""
        try:
            print(f"{Fore.CYAN}🚀 Proje Yönetim Sistemi başlatılıyor...")
            
            # ACP client'ları oluştur
            self.task_generator_client = Client(base_url="http://localhost:8004")
            self.scheduler_client = Client(base_url="http://localhost:8005")
            
            print(f"{Fore.GREEN}✅ Agent bağlantıları kuruldu")
            
            # Agent collection oluştur
            self.agent_collection = await AgentCollection.from_acp(
                self.task_generator_client,
                self.scheduler_client
            )
            
            # ACP agent'ları hazırla
            acp_agents = {
                agent.name: {
                    'agent': agent, 
                    'client': client
                } 
                for client, agent in self.agent_collection.agents
            }
            
            print(f"{Fore.GREEN}✅ Bulunan agent'lar: {list(acp_agents.keys())}")
            
            # ACP Calling Agent oluştur
            self.acp_agent = ACPCallingAgent(
                acp_agents=acp_agents,
                model=self.model
            )
            
            print(f"{Fore.GREEN}✅ Sistem hazır!")
            
        except Exception as e:
            print(f"{Fore.RED}❌ Başlatma hatası: {e}")
            raise
    
    async def cleanup(self):
        """Bağlantıları temizle"""
        try:
            if self.task_generator_client:
                await self.task_generator_client.close()
            if self.scheduler_client:
                await self.scheduler_client.close()
            print(f"{Fore.YELLOW}🧹 Bağlantılar temizlendi")
        except Exception as e:
            print(f"{Fore.RED}❌ Temizleme hatası: {e}")
    
    def get_project_idea(self):
        """Kullanıcıdan proje fikri al"""
        print(f"\n{Fore.CYAN}{'='*60}")
        print(f"{Fore.CYAN}🎯 PROJE YÖNETİM SİSTEMİ")
        print(f"{Fore.CYAN}{'='*60}")
        print(f"{Fore.WHITE}Proje fikrinizi detaylı olarak açıklayın:")
        print(f"{Fore.YELLOW}Örnek: 'E-ticaret web sitesi geliştirmek istiyorum. Kullanıcı kayıt/giriş, ürün kataloğu, sepet, ödeme sistemi olacak.'")
        print(f"{Fore.CYAN}{'-'*60}")
        
        project_idea = input(f"{Fore.WHITE}Proje Fikri: {Style.RESET_ALL}")
        return project_idea.strip()
    
    def display_tasks(self, tasks_json):
        """Task listesini güzel formatta göster"""
        try:
            tasks_data = json.loads(tasks_json)
            
            print(f"\n{Fore.GREEN}{'='*60}")
            print(f"{Fore.GREEN}📋 OLUŞTURULAN TASK LİSTESİ")
            print(f"{Fore.GREEN}{'='*60}")
            
            print(f"{Fore.CYAN}Proje: {tasks_data.get('project_name', 'Bilinmiyor')}")
            print(f"{Fore.CYAN}Toplam Tahmini Süre: {tasks_data.get('total_estimated_hours', 0)} saat")
            print(f"{Fore.CYAN}{'-'*60}")
            
            for task in tasks_data.get('tasks', []):
                print(f"\n{Fore.YELLOW}🔸 {task.get('id', 'N/A')}: {task.get('name', 'N/A')}")
                print(f"   📝 {task.get('description', 'N/A')}")
                print(f"   ⏱️  Süre: {task.get('estimated_hours', 0)} saat")
                print(f"   🎯 Öncelik: {task.get('priority', 'N/A')}")
                print(f"   🛠️  Beceriler: {', '.join(task.get('skills', []))}")
                print(f"   📂 Kategori: {task.get('category', 'N/A')}")
                if task.get('dependencies'):
                    print(f"   🔗 Bağımlılıklar: {', '.join(task.get('dependencies', []))}")
                    
        except json.JSONDecodeError:
            print(f"{Fore.RED}❌ Task listesi JSON formatında değil:")
            print(f"{Fore.WHITE}{tasks_json}")
    
    def display_schedule(self, schedule_json):
        """Zaman çizelgesini güzel formatta göster"""
        try:
            schedule_data = json.loads(schedule_json)
            
            print(f"\n{Fore.MAGENTA}{'='*60}")
            print(f"{Fore.MAGENTA}📅 ZAMAN ÇİZELGESİ")
            print(f"{Fore.MAGENTA}{'='*60}")
            
            print(f"{Fore.CYAN}Proje: {schedule_data.get('project_name', 'Bilinmiyor')}")
            print(f"{Fore.CYAN}Toplam Süre: {schedule_data.get('total_duration_days', 0)} gün")
            print(f"{Fore.CYAN}Günlük Çalışma: {schedule_data.get('working_hours_per_day', 8)} saat")
            print(f"{Fore.CYAN}{'-'*60}")
            
            for item in schedule_data.get('schedule', []):
                print(f"\n{Fore.YELLOW}📌 {item.get('task_id', 'N/A')}: {item.get('task_name', 'N/A')}")
                print(f"   📅 Tarih: {item.get('start_date', 'N/A')} ({item.get('day_of_week', 'N/A')})")
                print(f"   🕐 Saat: {item.get('start_time', 'N/A')} - {item.get('end_time', 'N/A')}")
                print(f"   ⏱️  Süre: {item.get('duration_hours', 0)} saat")
                if item.get('parallel_tasks'):
                    print(f"   🔄 Paralel: {', '.join(item.get('parallel_tasks', []))}")
            
            # Milestone'ları göster
            milestones = schedule_data.get('milestones', [])
            if milestones:
                print(f"\n{Fore.GREEN}🎯 MİLESTONE'LAR:")
                for milestone in milestones:
                    print(f"   🏁 {milestone.get('name', 'N/A')} - {milestone.get('date', 'N/A')}")
                    
        except json.JSONDecodeError:
            print(f"{Fore.RED}❌ Zaman çizelgesi JSON formatında değil:")
            print(f"{Fore.WHITE}{schedule_json}")
    
    async def run(self):
        """Ana çalışma döngüsü"""
        try:
            await self.initialize()
            
            while True:
                project_idea = self.get_project_idea()
                
                if not project_idea:
                    print(f"{Fore.YELLOW}⚠️  Proje fikri boş olamaz!")
                    continue
                
                if project_idea.lower() in ['exit', 'quit', 'çıkış']:
                    break
                
                print(f"\n{Fore.CYAN}🔄 Task'lar oluşturuluyor...")
                
                # Task'ları oluştur
                task_prompt = f"Bu proje fikri için detaylı task listesi oluştur: {project_idea}"
                tasks_result = await self.acp_agent.run(task_prompt)
                
                self.display_tasks(tasks_result)
                
                print(f"\n{Fore.CYAN}🔄 Zaman çizelgesi oluşturuluyor...")
                
                # Zaman çizelgesi oluştur
                schedule_prompt = f"Bu task listesi için zaman çizelgesi oluştur: {tasks_result}"
                schedule_result = await self.acp_agent.run(schedule_prompt)
                
                self.display_schedule(schedule_result)
                
                print(f"\n{Fore.GREEN}✅ Proje planı tamamlandı!")
                print(f"{Fore.YELLOW}Yeni proje için devam etmek ister misiniz? (Enter) veya çıkmak için 'exit' yazın.")
                
        except KeyboardInterrupt:
            print(f"\n{Fore.YELLOW}👋 Sistem kapatılıyor...")
        except Exception as e:
            print(f"{Fore.RED}❌ Hata: {e}")
        finally:
            await self.cleanup()

async def main():
    """Ana fonksiyon"""
    client = ProjectManagerClient()
    await client.run()

if __name__ == "__main__":
    asyncio.run(main())
