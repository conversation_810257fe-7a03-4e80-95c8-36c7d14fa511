# Import the implementation from the file
import asyncio 
from acp_sdk.client import Client
from smolagents import LiteLLMModel
from fastacp import AgentCollection, ACPCallingAgent, ActionStep
from colorama import Fore 
import os
from dotenv import load_dotenv
load_dotenv()

# OpenRouter API configuration
OPENROUTER_API_KEY = os.getenv("OPENROUTER_API_KEY")

model = LiteLLMModel(
        model_id="openrouter/openai/gpt-4o-mini",
        api_base="https://openrouter.ai/api/v1",
        api_key=OPENROUTER_API_KEY,
        num_ctx=4000)

async def run_project_workflow() -> None:
    async with <PERSON><PERSON>(base_url="http://localhost:8001") as task_gen_client, Client(base_url="http://localhost:8003") as time_plan_client:
        agent_collection = await AgentCollection.from_acp(task_gen_client, time_plan_client)
        acp_agents = {agent.name: {'agent':agent, 'client':client} for client, agent in agent_collection.agents}
        print(Fore.CYAN + "Available Agents:" + Fore.RESET)
        for name, info in acp_agents.items():
            print(f"  - {name}: {info['agent'].description}")

        acpagent = ACPCallingAgent(acp_agents=acp_agents, model=model)

        print(Fore.BLUE + "\n🚀 Proje planlaması başlıyor..." + Fore.RESET)
        print(Fore.BLUE + "=" * 50 + Fore.RESET)

        result = await acpagent.run("""
        Mikro CRM yapmak istiyorum.

        Bana bu proje için detaylı bir plan hazırlar mısın?
        Task listesi ve zaman planlaması dahil olmak üzere.

        Tamamladığında sonucu güzel bir formatta sun.
        """)

        print(Fore.YELLOW + "\n" + "=" * 50 + Fore.RESET)
        print(Fore.YELLOW + "📋 PROJE PLANI HAZIR!" + Fore.RESET)
        print(Fore.YELLOW + "=" * 50 + Fore.RESET)
        print(result)
        print(Fore.YELLOW + "=" * 50 + Fore.RESET)

if __name__ == '__main__':
    asyncio.run(run_project_workflow())